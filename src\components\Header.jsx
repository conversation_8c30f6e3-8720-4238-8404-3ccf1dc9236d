import React from 'react';
import { Search, Menu, X, Download, <PERSON>, <PERSON> } from 'lucide-react';
import { useAppContext } from '../../App.jsx';
import pdfFile from '../../assets/الدليلالعمليلمعاييرAAMIفيالهندسةالسريرية.pdf';

const Header = () => {
  const { 
    darkMode, 
    setDarkMode, 
    sidebarOpen, 
    setSidebarOpen, 
    searchTerm, 
    setSearchTerm 
  } = useAppContext();

  return (
    <header className="bg-card border-b border-border shadow-sm sticky top-0 z-40" role="banner">
      <div className="flex items-center justify-between p-4">
        <div className="flex items-center gap-4">
          <button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="p-2 hover:bg-accent rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-ring"
            aria-label={sidebarOpen ? "إغلاق الشريط الجانبي" : "فتح الشريط الجانبي"}
            aria-expanded={sidebarOpen}
          >
            {sidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </button>
          <div>
            <h1 className="text-xl font-bold text-primary">الدليل العملي لمعايير AAMI</h1>
            <p className="text-sm text-muted-foreground">في الهندسة السريرية</p>
          </div>
        </div>
        
        <div className="flex items-center gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="البحث في الكتاب..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-4 pr-10 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-ring w-64"
              aria-label="البحث في محتوى الكتاب"
            />
          </div>
          
          {/* Theme Toggle */}
          <button
            onClick={() => setDarkMode(!darkMode)}
            className="p-2 hover:bg-accent rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-ring"
            aria-label={darkMode ? "التبديل إلى الوضع الفاتح" : "التبديل إلى الوضع المظلم"}
          >
            {darkMode ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
          </button>
          
          {/* Download */}
          <a
            href={pdfFile}
            download="AAMI_Clinical_Engineering_Guide.pdf"
            className="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors focus:outline-none focus:ring-2 focus:ring-ring"
            aria-label="تحميل ملف PDF الكامل"
          >
            <Download className="h-4 w-4" />
            تحميل PDF
          </a>
        </div>
      </div>
    </header>
  );
};

export default Header;
