import React from 'react';
import { GlossaryTooltip, glossaryTerms } from './Glossary.jsx';

const GlossaryText = ({ children, className = '' }) => {
  if (typeof children !== 'string') {
    return <span className={className}>{children}</span>;
  }

  // Create a regex pattern that matches all glossary terms
  const terms = Object.keys(glossaryTerms);
  const pattern = new RegExp(`\\b(${terms.join('|')})\\b`, 'gi');
  
  // Split the text by glossary terms while preserving the terms
  const parts = children.split(pattern);
  
  return (
    <span className={className}>
      {parts.map((part, index) => {
        // Check if this part is a glossary term
        const isGlossaryTerm = terms.some(term => 
          term.toLowerCase() === part.toLowerCase()
        );
        
        if (isGlossaryTerm) {
          // Find the exact term with correct casing
          const exactTerm = terms.find(term => 
            term.toLowerCase() === part.toLowerCase()
          );
          
          return (
            <GlossaryTooltip key={index} term={exactTerm}>
              {part}
            </GlossaryTooltip>
          );
        }
        
        return part;
      })}
    </span>
  );
};

export default GlossaryText;
