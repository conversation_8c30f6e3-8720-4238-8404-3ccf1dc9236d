import React, { useState } from 'react';
import { 
  ShoppingC<PERSON>, 
  Settings, 
  Wrench, 
  Bar<PERSON>hart3, 
  Trash2, 
  Download,
  X,
  FileText
} from 'lucide-react';

const LifecycleCircle = () => {
  const [selectedStage, setSelectedStage] = useState(null);
  const [showModal, setShowModal] = useState(false);

  const stages = [
    {
      id: 1,
      title: 'التخطيط والشراء',
      subtitle: 'Planning & Procurement',
      icon: <ShoppingCart className="h-8 w-8" />,
      color: '#3b82f6', // blue-500
      angle: 0,
      description: 'تحديد الاحتياجات، تقييم الخيارات، والشراء المدروس للأجهزة الطبية',
      activities: [
        'تحليل الاحتياجات التقنية والسريرية',
        'تقييم الموردين والمنتجات',
        'إعد<PERSON> المواصفات التقنية',
        'عملية المناقصة والشراء'
      ],
      checklist: 'planning_procurement_checklist.pdf'
    },
    {
      id: 2,
      title: 'القبول والتركيب',
      subtitle: 'Acceptance & Installation',
      icon: <Settings className="h-8 w-8" />,
      color: '#22c55e', // green-500
      angle: 72,
      description: 'استلام الأجهزة، فحصها، تركيبها، وإجراء اختبارات القبول',
      activities: [
        'فحص الأجهزة عند الاستلام',
        'التركيب والتشغيل الأولي',
        'اختبارات الأداء والسلامة',
        'التدريب على الاستخدام'
      ],
      checklist: 'acceptance_installation_checklist.pdf'
    },
    {
      id: 3,
      title: 'الصيانة الوقائية',
      subtitle: 'Preventive Maintenance',
      icon: <Wrench className="h-8 w-8" />,
      color: '#f59e0b', // amber-500
      angle: 144,
      description: 'الصيانة المجدولة والفحوصات الدورية لضمان الأداء الأمثل',
      activities: [
        'جدولة الصيانة الدورية',
        'فحوصات السلامة الكهربائية',
        'معايرة الأجهزة',
        'استبدال القطع الاستهلاكية'
      ],
      checklist: 'preventive_maintenance_checklist.pdf'
    },
    {
      id: 4,
      title: 'المراقبة والتقييم',
      subtitle: 'Monitoring & Evaluation',
      icon: <BarChart3 className="h-8 w-8" />,
      color: '#8b5cf6', // violet-500
      angle: 216,
      description: 'مراقبة الأداء، تحليل البيانات، وتقييم فعالية الأجهزة',
      activities: [
        'مراقبة مؤشرات الأداء',
        'تحليل أعطال الأجهزة',
        'تقييم رضا المستخدمين',
        'إعداد التقارير الدورية'
      ],
      checklist: 'monitoring_evaluation_checklist.pdf'
    },
    {
      id: 5,
      title: 'الاستبدال والتخلص',
      subtitle: 'Replacement & Disposal',
      icon: <Trash2 className="h-8 w-8" />,
      color: '#ef4444', // red-500
      angle: 288,
      description: 'تقييم نهاية العمر الافتراضي والتخلص الآمن من الأجهزة',
      activities: [
        'تقييم نهاية العمر الافتراضي',
        'التخطيط للاستبدال',
        'التخلص الآمن والبيئي',
        'نقل البيانات والوثائق'
      ],
      checklist: 'replacement_disposal_checklist.pdf'
    }
  ];

  const handleStageClick = (stage) => {
    setSelectedStage(stage);
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedStage(null);
  };

  const downloadChecklist = (filename) => {
    // In a real implementation, this would download the actual PDF
    alert(`تحميل قائمة التحقق: ${filename}`);
  };

  // Calculate position for each stage
  const getStagePosition = (angle, radius = 120) => {
    const radian = (angle - 90) * (Math.PI / 180); // -90 to start from top
    const x = 200 + radius * Math.cos(radian);
    const y = 200 + radius * Math.sin(radian);
    return { x, y };
  };

  return (
    <div className="w-full p-6 bg-card rounded-lg border border-border">
      <h3 className="text-xl font-bold text-primary mb-6 text-center">
        دورة حياة إدارة التكنولوجيا الطبية
      </h3>
      
      {/* Circular Diagram */}
      <div className="flex justify-center mb-6">
        <svg width="400" height="400" viewBox="0 0 400 400" className="max-w-full h-auto">
          {/* Center Circle */}
          <circle
            cx="200"
            cy="200"
            r="60"
            fill="var(--primary)"
            className="drop-shadow-lg"
          />
          <text
            x="200"
            y="195"
            textAnchor="middle"
            className="fill-white text-sm font-bold"
          >
            إدارة دورة
          </text>
          <text
            x="200"
            y="210"
            textAnchor="middle"
            className="fill-white text-sm font-bold"
          >
            الحياة
          </text>
          
          {/* Stage Circles and Connections */}
          {stages.map((stage, index) => {
            const position = getStagePosition(stage.angle);
            const nextStage = stages[(index + 1) % stages.length];
            const nextPosition = getStagePosition(nextStage.angle);
            
            return (
              <g key={stage.id}>
                {/* Connection Line */}
                <line
                  x1={position.x}
                  y1={position.y}
                  x2={nextPosition.x}
                  y2={nextPosition.y}
                  stroke="var(--border)"
                  strokeWidth="2"
                  className="opacity-50"
                />
                
                {/* Stage Circle */}
                <g
                  onClick={() => handleStageClick(stage)}
                  className="cursor-pointer group"
                  role="button"
                  tabIndex={0}
                  aria-label={`${stage.title} - انقر لعرض التفاصيل`}
                >
                  <circle
                    cx={position.x}
                    cy={position.y}
                    r="35"
                    fill={stage.color}
                    className="transition-all duration-300 group-hover:r-40 drop-shadow-lg"
                  />
                  <foreignObject 
                    x={position.x - 20} 
                    y={position.y - 20} 
                    width="40" 
                    height="40"
                  >
                    <div className="flex items-center justify-center w-full h-full text-white">
                      {stage.icon}
                    </div>
                  </foreignObject>
                  
                  {/* Stage Label */}
                  <text
                    x={position.x}
                    y={position.y + 55}
                    textAnchor="middle"
                    className="fill-current text-xs font-semibold group-hover:text-primary transition-colors"
                  >
                    {stage.title}
                  </text>
                </g>
              </g>
            );
          })}
        </svg>
      </div>
      
      <div className="text-center text-sm text-muted-foreground">
        انقر على أي مرحلة لعرض التفاصيل وتحميل قائمة التحقق
      </div>
      
      {/* Modal */}
      {showModal && selectedStage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-card rounded-lg border border-border max-w-2xl w-full max-h-[80vh] overflow-y-auto animate-fade-in">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div 
                    className="w-12 h-12 rounded-full flex items-center justify-center text-white"
                    style={{ backgroundColor: selectedStage.color }}
                  >
                    {selectedStage.icon}
                  </div>
                  <div>
                    <h4 className="text-xl font-bold text-primary">
                      {selectedStage.title}
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {selectedStage.subtitle}
                    </p>
                  </div>
                </div>
                <button
                  onClick={closeModal}
                  className="p-2 hover:bg-accent rounded-lg transition-colors"
                  aria-label="إغلاق النافذة"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
              
              <p className="text-muted-foreground mb-6">
                {selectedStage.description}
              </p>
              
              <h5 className="text-lg font-semibold text-primary mb-4">
                الأنشطة الرئيسية:
              </h5>
              
              <ul className="space-y-2 mb-6">
                {selectedStage.activities.map((activity, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <div className="w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0"></div>
                    <span className="text-sm text-foreground">{activity}</span>
                  </li>
                ))}
              </ul>
              
              <div className="bg-muted p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-primary" />
                    <span className="font-semibold text-foreground">
                      قائمة التحقق لهذه المرحلة
                    </span>
                  </div>
                  <button
                    onClick={() => downloadChecklist(selectedStage.checklist)}
                    className="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
                  >
                    <Download className="h-4 w-4" />
                    تحميل PDF
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LifecycleCircle;
