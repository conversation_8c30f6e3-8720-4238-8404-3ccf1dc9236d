import React, { useState, useEffect } from 'react';
import { Search, BookOpen, Menu, X, Download, Sun, Moon, ChevronLeft, ChevronRight, ZoomIn, ZoomOut, RotateCcw } from 'lucide-react';
import './App.css';

// Import PDF file
import pdfFile from './assets/الدليلالعمليلمعاييرAAMIفيالهندسةالسريرية.pdf';

function App() {
  const [darkMode, setDarkMode] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages] = useState(51);
  const [searchTerm, setSearchTerm] = useState('');
  const [zoomLevel, setZoomLevel] = useState(100);

  // Table of contents structure
  const tableOfContents = [
    {
      id: 1,
      title: 'الجزء الأول: الأسس والمبادئ',
      pages: '1-20',
      children: [
        { id: 2, title: 'الفصل الأول: رحلة AAMI عبر الزمن', pages: '1-10' },
        { id: 3, title: 'مقدمة: لماذا يهمنا التاريخ؟', pages: '2-3' },
        { id: 4, title: 'المرحلة الأولى: عصر التأسيس والتركيز على السلامة المادية', pages: '4-8' },
        { id: 5, title: 'المرحلة الثانية: التوسع نحو القيادة العالمية وإدارة الأنظمة', pages: '9-15' }
      ]
    },
    {
      id: 6,
      title: 'الجزء الثاني: التطبيقات العملية',
      pages: '21-40',
      children: [
        { id: 7, title: 'معايير السلامة الكهربائية', pages: '21-30' },
        { id: 8, title: 'إدارة الأجهزة الطبية', pages: '31-40' }
      ]
    },
    {
      id: 9,
      title: 'الجزء الثالث: المراجع والملاحق',
      pages: '41-51',
      children: [
        { id: 10, title: 'المراجع', pages: '41-45' },
        { id: 11, title: 'الملاحق', pages: '46-51' }
      ]
    }
  ];

  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [darkMode]);

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const handleZoom = (action) => {
    if (action === 'in' && zoomLevel < 200) {
      setZoomLevel(zoomLevel + 25);
    } else if (action === 'out' && zoomLevel > 50) {
      setZoomLevel(zoomLevel - 25);
    } else if (action === 'reset') {
      setZoomLevel(100);
    }
  };

  const TableOfContentsItem = ({ item, level = 0 }) => (
    <div className={`${level > 0 ? 'mr-4' : ''}`}>
      <div 
        className="flex items-center justify-between p-2 hover:bg-accent rounded-lg cursor-pointer transition-colors"
        onClick={() => {
          const pageNum = parseInt(item.pages.split('-')[0]);
          handlePageChange(pageNum);
        }}
      >
        <div className="flex items-center gap-2">
          <BookOpen className="h-4 w-4 text-primary" />
          <span className="text-sm font-medium">{item.title}</span>
        </div>
        <span className="text-xs text-muted-foreground">{item.pages}</span>
      </div>
      {item.children && (
        <div className="mr-2">
          {item.children.map(child => (
            <TableOfContentsItem key={child.id} item={child} level={level + 1} />
          ))}
        </div>
      )}
    </div>
  );

  return (
    <div className="min-h-screen bg-background text-foreground" dir="rtl">
      {/* Header */}
      <header className="bg-card border-b border-border shadow-sm">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="p-2 hover:bg-accent rounded-lg transition-colors"
            >
              {sidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </button>
            <div>
              <h1 className="text-xl font-bold text-primary">الدليل العملي لمعايير AAMI</h1>
              <p className="text-sm text-muted-foreground">في الهندسة السريرية</p>
            </div>
          </div>
          
          <div className="flex items-center gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <input
                type="text"
                placeholder="البحث في الكتاب..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-4 pr-10 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-ring w-64"
              />
            </div>
            
            {/* Theme Toggle */}
            <button
              onClick={() => setDarkMode(!darkMode)}
              className="p-2 hover:bg-accent rounded-lg transition-colors"
            >
              {darkMode ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
            </button>
            
            {/* Download */}
            <a
              href={pdfFile}
              download="AAMI_Clinical_Engineering_Guide.pdf"
              className="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
            >
              <Download className="h-4 w-4" />
              تحميل PDF
            </a>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        {sidebarOpen && (
          <aside className="w-80 bg-card border-l border-border h-[calc(100vh-73px)] overflow-y-auto">
            <div className="p-4">
              <h2 className="text-lg font-semibold mb-4 text-primary">فهرس المحتويات</h2>
              <div className="space-y-1">
                {tableOfContents.map(item => (
                  <TableOfContentsItem key={item.id} item={item} />
                ))}
              </div>
              
              <div className="mt-8 p-4 bg-muted rounded-lg">
                <h3 className="font-semibold mb-2">معلومات الكتاب</h3>
                <div className="text-sm text-muted-foreground space-y-1">
                  <p><strong>المؤلف:</strong> د. محمد يعقوب اسماعيل</p>
                  <p><strong>الجامعة:</strong> جامعة السودان للعلوم والتكنولوجيا</p>
                  <p><strong>القسم:</strong> الهندسة الطبية الحيوية</p>
                  <p><strong>السنة:</strong> 2025</p>
                  <p><strong>عدد الصفحات:</strong> {totalPages}</p>
                </div>
              </div>
            </div>
          </aside>
        )}

        {/* Main Content */}
        <main className="flex-1 bg-muted/30">
          {/* PDF Viewer Controls */}
          <div className="bg-card border-b border-border p-4 flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="p-2 hover:bg-accent rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronRight className="h-5 w-5" />
              </button>
              
              <div className="flex items-center gap-2">
                <span className="text-sm">صفحة</span>
                <input
                  type="number"
                  value={currentPage}
                  onChange={(e) => handlePageChange(parseInt(e.target.value))}
                  className="w-16 px-2 py-1 text-center bg-background border border-border rounded"
                  min="1"
                  max={totalPages}
                />
                <span className="text-sm">من {totalPages}</span>
              </div>
              
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="p-2 hover:bg-accent rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronLeft className="h-5 w-5" />
              </button>
            </div>
            
            <div className="flex items-center gap-2">
              <button
                onClick={() => handleZoom('out')}
                className="p-2 hover:bg-accent rounded-lg transition-colors"
              >
                <ZoomOut className="h-4 w-4" />
              </button>
              <span className="text-sm w-16 text-center">{zoomLevel}%</span>
              <button
                onClick={() => handleZoom('in')}
                className="p-2 hover:bg-accent rounded-lg transition-colors"
              >
                <ZoomIn className="h-4 w-4" />
              </button>
              <button
                onClick={() => handleZoom('reset')}
                className="p-2 hover:bg-accent rounded-lg transition-colors"
              >
                <RotateCcw className="h-4 w-4" />
              </button>
            </div>
          </div>

          {/* PDF Viewer */}
          <div className="p-4 flex justify-center">
            <div 
              className="bg-white shadow-lg rounded-lg overflow-hidden pdf-container"
              style={{ transform: `scale(${zoomLevel / 100})`, transformOrigin: 'top center' }}
            >
              <div className="w-[800px] h-[1000px] bg-white border border-gray-200 flex items-center justify-center">
                <div className="text-center p-8">
                  <BookOpen className="h-16 w-16 text-primary mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-primary mb-2">الدليل العملي لمعايير AAMI</h3>
                  <p className="text-muted-foreground mb-4">في الهندسة السريرية</p>
                  <p className="text-sm text-muted-foreground mb-6">
                    صفحة {currentPage} من {totalPages}
                  </p>
                  <div className="bg-muted p-6 rounded-lg text-right">
                    <h4 className="font-semibold mb-3">محتوى الصفحة الحالية:</h4>
                    {currentPage === 1 && (
                      <div className="space-y-2 text-sm">
                        <p><strong>العنوان:</strong> الدليل العملي لمعايير AAMI في الهندسة السريرية</p>
                        <p><strong>المؤلف:</strong> د. محمد يعقوب اسماعيل</p>
                        <p><strong>المؤسسة:</strong> قسم الهندسة الطبية الحيوية، جامعة السودان للعلوم والتكنولوجيا (SUST)</p>
                        <p><strong>السنة:</strong> 2025</p>
                        <p><strong>الجزء الأول:</strong> الأسس والمبادئ</p>
                      </div>
                    )}
                    {currentPage === 2 && (
                      <div className="space-y-2 text-sm">
                        <p><strong>الفصل الأول:</strong> رحلة AAMI عبر الزمن</p>
                        <p><strong>أهداف التعلم:</strong></p>
                        <ul className="list-disc list-inside space-y-1 mr-4">
                          <li>وصف السياق التاريخي الذي أدى إلى تأسيس AAMI</li>
                          <li>تحديد المحطات الرئيسية في تطور برنامج معايير AAMI</li>
                          <li>ربط كل مرحلة من مراحل تطور AAMI بالتطورات الموازية في التكنولوجيا الطبية ودور المهندس السريري</li>
                        </ul>
                      </div>
                    )}
                    {currentPage > 2 && (
                      <div className="space-y-2 text-sm">
                        <p>هذه الصفحة تحتوي على محتوى من الكتاب الأصلي.</p>
                        <p>يمكنك تحميل ملف PDF الكامل للاطلاع على المحتوى التفصيلي.</p>
                      </div>
                    )}
                  </div>
                  <div className="mt-6">
                    <a
                      href={pdfFile}
                      download="AAMI_Clinical_Engineering_Guide.pdf"
                      className="inline-flex items-center gap-2 px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
                    >
                      <Download className="h-4 w-4" />
                      تحميل الملف الكامل
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}

export default App;

