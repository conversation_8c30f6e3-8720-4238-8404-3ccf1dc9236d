import React, { useState, useEffect, createContext, useContext } from 'react';
import { Search, BookOpen, Menu, X, Download, Sun, Moon, ChevronLeft, ChevronRight, ZoomIn, ZoomOut, RotateCcw } from 'lucide-react';
import './App.css';

// Import PDF file
import pdfFile from './assets/الدليلالعمليلمعاييرAAMIفيالهندسةالسريرية.pdf';

// Create App Context for global state management
const AppContext = createContext();

// Custom hook to use app context
export const useAppContext = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within AppProvider');
  }
  return context;
};

// Import components
import Header from './src/components/Header.jsx';
import Sidebar from './src/components/Sidebar.jsx';
import MainContent from './src/components/MainContent.jsx';
import Footer from './src/components/Footer.jsx';

function App() {
  const [darkMode, setDarkMode] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages] = useState(51);
  const [searchTerm, setSearchTerm] = useState('');
  const [zoomLevel, setZoomLevel] = useState(100);

  // Table of contents structure
  const tableOfContents = [
    {
      id: 1,
      title: 'الجزء الأول: الأسس والمبادئ',
      pages: '1-20',
      children: [
        { id: 2, title: 'الفصل الأول: رحلة AAMI عبر الزمن', pages: '1-10' },
        { id: 3, title: 'مقدمة: لماذا يهمنا التاريخ؟', pages: '2-3' },
        { id: 4, title: 'المرحلة الأولى: عصر التأسيس والتركيز على السلامة المادية', pages: '4-8' },
        { id: 5, title: 'المرحلة الثانية: التوسع نحو القيادة العالمية وإدارة الأنظمة', pages: '9-15' }
      ]
    },
    {
      id: 6,
      title: 'الجزء الثاني: التطبيقات العملية',
      pages: '21-40',
      children: [
        { id: 7, title: 'معايير السلامة الكهربائية', pages: '21-30' },
        { id: 8, title: 'إدارة الأجهزة الطبية', pages: '31-40' }
      ]
    },
    {
      id: 9,
      title: 'الجزء الثالث: المراجع والملاحق',
      pages: '41-51',
      children: [
        { id: 10, title: 'المراجع', pages: '41-45' },
        { id: 11, title: 'الملاحق', pages: '46-51' }
      ]
    }
  ];

  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [darkMode]);

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const handleZoom = (action) => {
    if (action === 'in' && zoomLevel < 200) {
      setZoomLevel(zoomLevel + 25);
    } else if (action === 'out' && zoomLevel > 50) {
      setZoomLevel(zoomLevel - 25);
    } else if (action === 'reset') {
      setZoomLevel(100);
    }
  };

  // Context value with all shared state and functions
  const contextValue = {
    darkMode,
    setDarkMode,
    sidebarOpen,
    setSidebarOpen,
    currentPage,
    setCurrentPage,
    totalPages,
    searchTerm,
    setSearchTerm,
    zoomLevel,
    setZoomLevel,
    tableOfContents,
    handlePageChange,
    handleZoom
  };

  return (
    <AppContext.Provider value={contextValue}>
      <div className="app-grid min-h-screen bg-background text-foreground" dir="rtl">
        <Header />
        <div className="app-body">
          <Sidebar />
          <MainContent />
        </div>
        <Footer />
      </div>
    </AppContext.Provider>
  );
}

export default App;

