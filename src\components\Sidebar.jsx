import React, { useState } from 'react';
import { BookOpen, Book } from 'lucide-react';
import { useAppContext } from '../../App.jsx';
import EnhancedTableOfContents from './EnhancedTableOfContents.jsx';
import { GlossaryPanel } from './Glossary.jsx';



const Sidebar = () => {
  const { sidebarOpen, totalPages } = useAppContext();
  const [glossaryOpen, setGlossaryOpen] = useState(false);

  if (!sidebarOpen) return null;

  return (
    <>
      <aside
        className="w-80 bg-card border-l border-border h-[calc(100vh-73px)] overflow-y-auto"
        role="complementary"
        aria-label="فهرس المحتويات والمعلومات"
      >
        <div className="p-4 space-y-6">
          {/* Enhanced Table of Contents */}
          <EnhancedTableOfContents />

          {/* Glossary Button */}
          <div className="border-t border-border pt-4">
            <button
              onClick={() => setGlossaryOpen(true)}
              className="w-full flex items-center gap-3 p-3 bg-primary/10 hover:bg-primary/20 text-primary rounded-lg transition-colors"
              aria-label="فتح قاموس المصطلحات"
            >
              <Book className="h-5 w-5" />
              <span className="font-medium">قاموس المصطلحات</span>
            </button>
          </div>

          {/* Book Information */}
          <div className="p-4 bg-muted rounded-lg">
            <h3 className="font-semibold mb-2">معلومات الكتاب</h3>
            <div className="text-sm text-muted-foreground space-y-1">
              <p><strong>المؤلف:</strong> د. محمد يعقوب اسماعيل</p>
              <p><strong>الجامعة:</strong> جامعة السودان للعلوم والتكنولوجيا</p>
              <p><strong>القسم:</strong> الهندسة الطبية الحيوية</p>
              <p><strong>السنة:</strong> 2025</p>
              <p><strong>عدد الصفحات:</strong> {totalPages}</p>
            </div>
          </div>
        </div>
      </aside>

      {/* Glossary Panel */}
      <GlossaryPanel
        isOpen={glossaryOpen}
        onClose={() => setGlossaryOpen(false)}
      />
    </>
  );
};

export default Sidebar;
