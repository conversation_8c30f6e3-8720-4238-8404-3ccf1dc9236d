import React, { useState } from 'react';
import { Shield, Target, Users, X } from 'lucide-react';

const ThreePillarsTriangle = () => {
  const [selectedPillar, setSelectedPillar] = useState(null);
  const [showModal, setShowModal] = useState(false);

  const pillars = {
    safety: {
      id: 'safety',
      title: 'السلامة (Safety)',
      icon: <Shield className="h-8 w-8" />,
      color: '#ef4444', // red-500
      description: 'ضمان أن الأجهزة الطبية آمنة للاستخدام ولا تسبب ضرراً للمرضى أو المشغلين',
      standards: [
        { code: 'IEC 60601', name: 'السلامة الكهربائية للأجهزة الطبية', description: 'المعيار الأساسي للسلامة الكهربائية' },
        { code: 'ISO 10993', name: 'التقييم البيولوجي للأجهزة الطبية', description: 'تقييم التوافق الحيوي للمواد' },
        { code: 'ISO 14971', name: 'إدارة المخاطر للأجهزة الطبية', description: 'منهجية شاملة لإدارة المخاطر' },
        { code: 'IEC 62304', name: 'برمجيات الأجهزة الطبية', description: 'دورة حياة تطوير البرمجيات الطبية' }
      ]
    },
    effectiveness: {
      id: 'effectiveness',
      title: 'الفعالية (Effectiveness)',
      icon: <Target className="h-8 w-8" />,
      color: '#22c55e', // green-500
      description: 'التأكد من أن الأجهزة الطبية تؤدي وظيفتها المقصودة بكفاءة وفعالية',
      standards: [
        { code: 'ISO 13485', name: 'نظم إدارة الجودة للأجهزة الطبية', description: 'متطلبات نظام إدارة الجودة' },
        { code: 'IEC 62366', name: 'قابلية الاستخدام للأجهزة الطبية', description: 'تصميم واجهات المستخدم الآمنة' },
        { code: 'ISO 14155', name: 'التحقيقات السريرية للأجهزة الطبية', description: 'إرشادات الممارسة السريرية الجيدة' },
        { code: 'IEC 80001', name: 'شبكات تكنولوجيا المعلومات الطبية', description: 'إدارة مخاطر الشبكات الطبية' }
      ]
    },
    access: {
      id: 'access',
      title: 'إمكانية الوصول (Access)',
      icon: <Users className="h-8 w-8" />,
      color: '#3b82f6', // blue-500
      description: 'ضمان توفر الأجهزة الطبية وإمكانية الوصول إليها لجميع المرضى والمؤسسات الصحية',
      standards: [
        { code: 'ISO 15223', name: 'الرموز المستخدمة مع ملصقات الأجهزة الطبية', description: 'توحيد الرموز والملصقات' },
        { code: 'IEC 62304', name: 'برمجيات الأجهزة الطبية', description: 'ضمان جودة البرمجيات الطبية' },
        { code: 'ISO 27799', name: 'أمن المعلومات الصحية', description: 'حماية البيانات الطبية' },
        { code: 'AAMI TIR57', name: 'إدارة دورة حياة الأجهزة الطبية', description: 'إرشادات إدارة الأجهزة في المستشفيات' }
      ]
    }
  };

  const handlePillarClick = (pillarId) => {
    setSelectedPillar(pillars[pillarId]);
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedPillar(null);
  };

  return (
    <div className="w-full p-6 bg-card rounded-lg border border-border">
      <h3 className="text-xl font-bold text-primary mb-6 text-center">
        الأركان الثلاثة لفلسفة AAMI
      </h3>
      
      {/* SVG Triangle */}
      <div className="flex justify-center mb-6">
        <svg width="400" height="350" viewBox="0 0 400 350" className="max-w-full h-auto">
          {/* Triangle Background */}
          <path
            d="M200 50 L350 280 L50 280 Z"
            fill="none"
            stroke="var(--border)"
            strokeWidth="2"
            className="transition-all duration-300"
          />
          
          {/* Safety - Top */}
          <g
            onClick={() => handlePillarClick('safety')}
            className="cursor-pointer group"
            role="button"
            tabIndex={0}
            aria-label="السلامة - انقر لعرض المعايير المرتبطة"
          >
            <circle
              cx="200"
              cy="80"
              r="40"
              fill={pillars.safety.color}
              className="transition-all duration-300 group-hover:r-45 drop-shadow-lg"
            />
            <foreignObject x="175" y="55" width="50" height="50">
              <div className="flex items-center justify-center w-full h-full text-white">
                {pillars.safety.icon}
              </div>
            </foreignObject>
            <text
              x="200"
              y="140"
              textAnchor="middle"
              className="fill-current text-sm font-semibold group-hover:text-primary transition-colors"
            >
              السلامة
            </text>
          </g>
          
          {/* Effectiveness - Bottom Left */}
          <g
            onClick={() => handlePillarClick('effectiveness')}
            className="cursor-pointer group"
            role="button"
            tabIndex={0}
            aria-label="الفعالية - انقر لعرض المعايير المرتبطة"
          >
            <circle
              cx="120"
              cy="250"
              r="40"
              fill={pillars.effectiveness.color}
              className="transition-all duration-300 group-hover:r-45 drop-shadow-lg"
            />
            <foreignObject x="95" y="225" width="50" height="50">
              <div className="flex items-center justify-center w-full h-full text-white">
                {pillars.effectiveness.icon}
              </div>
            </foreignObject>
            <text
              x="120"
              y="310"
              textAnchor="middle"
              className="fill-current text-sm font-semibold group-hover:text-primary transition-colors"
            >
              الفعالية
            </text>
          </g>
          
          {/* Access - Bottom Right */}
          <g
            onClick={() => handlePillarClick('access')}
            className="cursor-pointer group"
            role="button"
            tabIndex={0}
            aria-label="إمكانية الوصول - انقر لعرض المعايير المرتبطة"
          >
            <circle
              cx="280"
              cy="250"
              r="40"
              fill={pillars.access.color}
              className="transition-all duration-300 group-hover:r-45 drop-shadow-lg"
            />
            <foreignObject x="255" y="225" width="50" height="50">
              <div className="flex items-center justify-center w-full h-full text-white">
                {pillars.access.icon}
              </div>
            </foreignObject>
            <text
              x="280"
              y="310"
              textAnchor="middle"
              className="fill-current text-sm font-semibold group-hover:text-primary transition-colors"
            >
              إمكانية الوصول
            </text>
          </g>
          
          {/* Center Text */}
          <text
            x="200"
            y="190"
            textAnchor="middle"
            className="fill-current text-lg font-bold text-primary"
          >
            AAMI
          </text>
        </svg>
      </div>
      
      <div className="text-center text-sm text-muted-foreground">
        انقر على أي ركن لعرض المعايير المرتبطة به
      </div>
      
      {/* Modal */}
      {showModal && selectedPillar && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-card rounded-lg border border-border max-w-2xl w-full max-h-[80vh] overflow-y-auto animate-fade-in">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div 
                    className="w-12 h-12 rounded-full flex items-center justify-center text-white"
                    style={{ backgroundColor: selectedPillar.color }}
                  >
                    {selectedPillar.icon}
                  </div>
                  <h4 className="text-xl font-bold text-primary">
                    {selectedPillar.title}
                  </h4>
                </div>
                <button
                  onClick={closeModal}
                  className="p-2 hover:bg-accent rounded-lg transition-colors"
                  aria-label="إغلاق النافذة"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
              
              <p className="text-muted-foreground mb-6">
                {selectedPillar.description}
              </p>
              
              <h5 className="text-lg font-semibold text-primary mb-4">
                المعايير الرئيسية:
              </h5>
              
              <div className="space-y-4">
                {selectedPillar.standards.map((standard, index) => (
                  <div key={index} className="p-4 bg-muted rounded-lg">
                    <div className="flex items-start gap-3">
                      <div className="bg-primary text-primary-foreground px-2 py-1 rounded text-xs font-mono">
                        {standard.code}
                      </div>
                      <div className="flex-1">
                        <h6 className="font-semibold text-foreground mb-1">
                          {standard.name}
                        </h6>
                        <p className="text-sm text-muted-foreground">
                          {standard.description}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ThreePillarsTriangle;
