import React from 'react';
import { useAppContext } from '../../App.jsx';
import InteractiveTimeline from './InteractiveTimeline.jsx';
import ThreePillarsTriangle from './ThreePillarsTriangle.jsx';
import LifecycleCircle from './LifecycleCircle.jsx';
import ImageGallery, { sampleImages } from './ImageGallery.jsx';
import DataVisualization from './DataVisualization.jsx';

const InteractiveContent = () => {
  const { currentPage } = useAppContext();

  // Define which interactive components to show based on page/chapter
  const getInteractiveComponent = () => {
    // Chapter 1: AAMI Journey (pages 1-10)
    if (currentPage >= 1 && currentPage <= 10) {
      return (
        <div className="space-y-8">
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 p-6 rounded-lg border border-blue-200 dark:border-blue-800">
            <h3 className="text-lg font-bold text-blue-900 dark:text-blue-100 mb-3">
              الفصل الأول: رحلة AAMI عبر الزمن
            </h3>
            <p className="text-blue-800 dark:text-blue-200 text-sm mb-4">
              استكشف المحطات الرئيسية في تطور جمعية النهوض بالأجهزة الطبية من خلال هذا الخط الزمني التفاعلي
            </p>
          </div>

          <InteractiveTimeline />

          <ImageGallery
            images={sampleImages.clinicalEngineering.slice(0, 3)}
            title="صور من تاريخ الهندسة السريرية"
            description="مجموعة من الصور التي تُظهر تطور مجال الهندسة السريرية عبر العقود"
          />
        </div>
      );
    }
    
    // Chapter 2: Three Pillars Philosophy (pages 11-20)
    if (currentPage >= 11 && currentPage <= 20) {
      return (
        <div className="space-y-8">
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 p-6 rounded-lg border border-green-200 dark:border-green-800">
            <h3 className="text-lg font-bold text-green-900 dark:text-green-100 mb-3">
              الفصل الثاني: فلسفة الأركان الثلاثة
            </h3>
            <p className="text-green-800 dark:text-green-200 text-sm mb-4">
              اكتشف الأركان الثلاثة الأساسية لفلسفة AAMI: السلامة، الفعالية، وإمكانية الوصول
            </p>
          </div>

          <ThreePillarsTriangle />

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <DataVisualization chartKey="riskLevels" />
            <ImageGallery
              images={sampleImages.clinicalEngineering.slice(1, 4)}
              title="تطبيقات السلامة في الأجهزة الطبية"
              description="أمثلة عملية على تطبيق معايير السلامة والفعالية"
              layout="grid"
            />
          </div>
        </div>
      );
    }
    
    // Chapter 6: Technology Lifecycle Management (pages 31-40)
    if (currentPage >= 31 && currentPage <= 40) {
      return (
        <div className="space-y-8">
          <div className="bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-950/20 dark:to-violet-950/20 p-6 rounded-lg border border-purple-200 dark:border-purple-800">
            <h3 className="text-lg font-bold text-purple-900 dark:text-purple-100 mb-3">
              الفصل السادس: إدارة دورة حياة التكنولوجيا
            </h3>
            <p className="text-purple-800 dark:text-purple-200 text-sm mb-4">
              تعلم المراحل الخمس لإدارة دورة حياة الأجهزة الطبية في المستشفيات
            </p>
          </div>

          <LifecycleCircle />

          <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
            <div className="space-y-6">
              <DataVisualization chartKey="deviceFailures" />
              <DataVisualization chartKey="maintenanceFrequency" />
            </div>

            <ImageGallery
              images={sampleImages.clinicalEngineering.slice(2, 6)}
              title="إدارة الأجهزة الطبية في الممارسة العملية"
              description="صور من بيئة العمل الحقيقية تُظهر تطبيق مبادئ إدارة دورة حياة الأجهزة الطبية"
            />
          </div>
        </div>
      );
    }
    
    // Default content for other pages
    return (
      <div className="space-y-6">
        <div className="bg-gradient-to-r from-gray-50 to-slate-50 dark:from-gray-950/20 dark:to-slate-950/20 p-6 rounded-lg border border-gray-200 dark:border-gray-800">
          <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-3">
            محتوى تفاعلي
          </h3>
          <p className="text-gray-800 dark:text-gray-200 text-sm mb-4">
            انتقل إلى الفصول التالية لاستكشاف المحتوى التفاعلي:
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div className="p-4 bg-blue-100 dark:bg-blue-900/30 rounded-lg border border-blue-200 dark:border-blue-800">
              <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                الفصل الأول
              </h4>
              <p className="text-sm text-blue-800 dark:text-blue-200">
                رحلة AAMI عبر الزمن (الصفحات 1-10)
              </p>
            </div>
            <div className="p-4 bg-green-100 dark:bg-green-900/30 rounded-lg border border-green-200 dark:border-green-800">
              <h4 className="font-semibold text-green-900 dark:text-green-100 mb-2">
                الفصل الثاني
              </h4>
              <p className="text-sm text-green-800 dark:text-green-200">
                فلسفة الأركان الثلاثة (الصفحات 11-20)
              </p>
            </div>
            <div className="p-4 bg-purple-100 dark:bg-purple-900/30 rounded-lg border border-purple-200 dark:border-purple-800">
              <h4 className="font-semibold text-purple-900 dark:text-purple-100 mb-2">
                الفصل السادس
              </h4>
              <p className="text-sm text-purple-800 dark:text-purple-200">
                إدارة دورة حياة التكنولوجيا (الصفحات 31-40)
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="w-full">
      {getInteractiveComponent()}
    </div>
  );
};

export default InteractiveContent;
