import React, { useState, useEffect } from 'react';
import { BookOpen, ChevronDown, ChevronLeft, Circle, CheckCircle } from 'lucide-react';
import { useAppContext } from '../../App.jsx';

const EnhancedTableOfContents = () => {
  const { currentPage, handlePageChange, tableOfContents } = useAppContext();
  const [expandedSections, setExpandedSections] = useState(new Set([1, 6, 9])); // Expand main sections by default
  const [readingProgress, setReadingProgress] = useState({});

  // Calculate reading progress based on current page
  useEffect(() => {
    const progress = {};
    tableOfContents.forEach(section => {
      const [startPage, endPage] = section.pages.split('-').map(Number);
      const totalPages = endPage - startPage + 1;
      const currentProgress = Math.max(0, Math.min(currentPage - startPage + 1, totalPages));
      progress[section.id] = {
        current: currentProgress,
        total: totalPages,
        percentage: Math.round((currentProgress / totalPages) * 100)
      };

      // Calculate progress for subsections
      if (section.children) {
        section.children.forEach(child => {
          const [childStart, childEnd] = child.pages.split('-').map(Number);
          const childTotal = childEnd - childStart + 1;
          const childProgress = Math.max(0, Math.min(currentPage - childStart + 1, childTotal));
          progress[child.id] = {
            current: childProgress,
            total: childTotal,
            percentage: Math.round((childProgress / childTotal) * 100)
          };
        });
      }
    });
    setReadingProgress(progress);
  }, [currentPage, tableOfContents]);

  const toggleSection = (sectionId) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  const isCurrentSection = (pages) => {
    const [startPage, endPage] = pages.split('-').map(Number);
    return currentPage >= startPage && currentPage <= endPage;
  };

  const getProgressIcon = (sectionId) => {
    const progress = readingProgress[sectionId];
    if (!progress) return <Circle className="h-3 w-3 text-muted-foreground" />;
    
    if (progress.percentage === 100) {
      return <CheckCircle className="h-3 w-3 text-green-500" />;
    } else if (progress.percentage > 0) {
      return (
        <div className="relative w-3 h-3">
          <Circle className="h-3 w-3 text-muted-foreground" />
          <div 
            className="absolute inset-0 rounded-full border-2 border-primary"
            style={{
              background: `conic-gradient(var(--primary) ${progress.percentage * 3.6}deg, transparent 0deg)`
            }}
          />
        </div>
      );
    }
    return <Circle className="h-3 w-3 text-muted-foreground" />;
  };

  const TableOfContentsItem = ({ item, level = 0, isChild = false }) => {
    const isExpanded = expandedSections.has(item.id);
    const isCurrent = isCurrentSection(item.pages);
    const hasChildren = item.children && item.children.length > 0;
    const progress = readingProgress[item.id];

    return (
      <div className={`${level > 0 ? 'mr-4' : ''}`}>
        <div 
          className={`
            flex items-center justify-between p-3 rounded-lg cursor-pointer transition-all duration-200
            ${isCurrent 
              ? 'bg-primary/10 border border-primary/20 text-primary' 
              : 'hover:bg-accent text-foreground'
            }
            ${isChild ? 'py-2' : ''}
          `}
          onClick={() => {
            if (hasChildren && !isChild) {
              toggleSection(item.id);
            }
            const pageNum = parseInt(item.pages.split('-')[0]);
            handlePageChange(pageNum);
          }}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              if (hasChildren && !isChild) {
                toggleSection(item.id);
              }
              const pageNum = parseInt(item.pages.split('-')[0]);
              handlePageChange(pageNum);
            }
          }}
          tabIndex={0}
          role="button"
          aria-label={`${item.title} - الصفحات ${item.pages}`}
          aria-expanded={hasChildren ? isExpanded : undefined}
        >
          <div className="flex items-center gap-3 flex-1 min-w-0">
            {/* Expand/Collapse Icon */}
            {hasChildren && !isChild && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  toggleSection(item.id);
                }}
                className="p-1 hover:bg-accent rounded transition-colors"
                aria-label={isExpanded ? 'طي القسم' : 'توسيع القسم'}
              >
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronLeft className="h-4 w-4" />
                )}
              </button>
            )}
            
            {/* Progress Icon */}
            <div className="flex-shrink-0">
              {getProgressIcon(item.id)}
            </div>
            
            {/* Book Icon for main sections */}
            {!isChild && (
              <BookOpen className="h-4 w-4 text-primary flex-shrink-0" />
            )}
            
            {/* Title */}
            <div className="flex-1 min-w-0">
              <span className={`text-sm font-medium block truncate ${isCurrent ? 'font-bold' : ''}`}>
                {item.title}
              </span>
              {progress && progress.percentage > 0 && (
                <div className="flex items-center gap-2 mt-1">
                  <div className="flex-1 bg-muted rounded-full h-1.5">
                    <div 
                      className="bg-primary rounded-full h-1.5 transition-all duration-300"
                      style={{ width: `${progress.percentage}%` }}
                    />
                  </div>
                  <span className="text-xs text-muted-foreground">
                    {progress.percentage}%
                  </span>
                </div>
              )}
            </div>
          </div>
          
          {/* Page Range */}
          <span className="text-xs text-muted-foreground flex-shrink-0 mr-2">
            {item.pages}
          </span>
        </div>
        
        {/* Children */}
        {hasChildren && isExpanded && (
          <div className="mr-2 mt-1 space-y-1 animate-fade-in">
            {item.children.map(child => (
              <TableOfContentsItem 
                key={child.id} 
                item={child} 
                level={level + 1} 
                isChild={true}
              />
            ))}
          </div>
        )}
      </div>
    );
  };

  // Calculate overall reading progress
  const overallProgress = tableOfContents.reduce((acc, section) => {
    const [startPage, endPage] = section.pages.split('-').map(Number);
    const totalPages = endPage - startPage + 1;
    const currentProgress = Math.max(0, Math.min(currentPage - startPage + 1, totalPages));
    return {
      current: acc.current + currentProgress,
      total: acc.total + totalPages
    };
  }, { current: 0, total: 0 });

  const overallPercentage = Math.round((overallProgress.current / overallProgress.total) * 100);

  return (
    <div className="space-y-4">
      {/* Overall Progress */}
      <div className="p-4 bg-muted rounded-lg">
        <h3 className="font-semibold text-foreground mb-3 flex items-center gap-2">
          <BookOpen className="h-4 w-4 text-primary" />
          تقدم القراءة
        </h3>
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">التقدم الإجمالي</span>
            <span className="font-medium text-foreground">{overallPercentage}%</span>
          </div>
          <div className="bg-background rounded-full h-2">
            <div 
              className="bg-primary rounded-full h-2 transition-all duration-500"
              style={{ width: `${overallPercentage}%` }}
            />
          </div>
          <div className="text-xs text-muted-foreground">
            الصفحة {currentPage} من {overallProgress.total}
          </div>
        </div>
      </div>

      {/* Table of Contents */}
      <div>
        <h2 className="text-lg font-semibold mb-4 text-primary">فهرس المحتويات</h2>
        <nav aria-label="فهرس المحتويات المحسن">
          <div className="space-y-2">
            {tableOfContents.map(item => (
              <TableOfContentsItem key={item.id} item={item} />
            ))}
          </div>
        </nav>
      </div>
    </div>
  );
};

export default EnhancedTableOfContents;
