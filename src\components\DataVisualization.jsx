import React, { useRef, useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TrendingUp, AlertTriangle } from 'lucide-react';

// Simple chart implementation without external dependencies
const SimpleChart = ({ data, type, title, description }) => {
  const canvasRef = useRef(null);
  const [hoveredIndex, setHoveredIndex] = useState(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || !data) return;

    const ctx = canvas.getContext('2d');
    const { width, height } = canvas;
    
    // Clear canvas
    ctx.clearRect(0, 0, width, height);
    
    if (type === 'bar') {
      drawBarChart(ctx, data, width, height);
    } else if (type === 'pie') {
      drawPieChart(ctx, data, width, height);
    }
  }, [data, type, hoveredIndex]);

  const drawBarChart = (ctx, data, width, height) => {
    const padding = 60;
    const chartWidth = width - 2 * padding;
    const chartHeight = height - 2 * padding;
    
    const maxValue = Math.max(...data.map(d => d.value));
    const barWidth = chartWidth / data.length * 0.8;
    const barSpacing = chartWidth / data.length * 0.2;

    // Draw bars
    data.forEach((item, index) => {
      const barHeight = (item.value / maxValue) * chartHeight;
      const x = padding + index * (barWidth + barSpacing);
      const y = height - padding - barHeight;

      // Bar color
      ctx.fillStyle = hoveredIndex === index ? item.hoverColor || '#3b82f6' : item.color || '#6b7280';
      ctx.fillRect(x, y, barWidth, barHeight);

      // Label
      ctx.fillStyle = '#374151';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(item.label, x + barWidth / 2, height - padding + 20);

      // Value
      ctx.fillStyle = '#111827';
      ctx.font = 'bold 14px Arial';
      ctx.fillText(item.value.toString(), x + barWidth / 2, y - 10);
    });

    // Y-axis
    ctx.strokeStyle = '#d1d5db';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, height - padding);
    ctx.stroke();

    // X-axis
    ctx.beginPath();
    ctx.moveTo(padding, height - padding);
    ctx.lineTo(width - padding, height - padding);
    ctx.stroke();
  };

  const drawPieChart = (ctx, data, width, height) => {
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) / 2 - 40;
    
    const total = data.reduce((sum, item) => sum + item.value, 0);
    let currentAngle = -Math.PI / 2; // Start from top

    data.forEach((item, index) => {
      const sliceAngle = (item.value / total) * 2 * Math.PI;
      
      // Draw slice
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
      ctx.closePath();
      ctx.fillStyle = hoveredIndex === index ? item.hoverColor || '#3b82f6' : item.color || '#6b7280';
      ctx.fill();
      
      // Draw border
      ctx.strokeStyle = '#ffffff';
      ctx.lineWidth = 2;
      ctx.stroke();

      // Draw label
      const labelAngle = currentAngle + sliceAngle / 2;
      const labelX = centerX + Math.cos(labelAngle) * (radius + 20);
      const labelY = centerY + Math.sin(labelAngle) * (radius + 20);
      
      ctx.fillStyle = '#374151';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(`${item.label}`, labelX, labelY);
      ctx.fillText(`${((item.value / total) * 100).toFixed(1)}%`, labelX, labelY + 15);

      currentAngle += sliceAngle;
    });
  };

  const handleMouseMove = (e) => {
    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    if (type === 'bar') {
      const padding = 60;
      const chartWidth = canvas.width - 2 * padding;
      const barWidth = chartWidth / data.length * 0.8;
      const barSpacing = chartWidth / data.length * 0.2;

      let newHoveredIndex = null;
      data.forEach((item, index) => {
        const barX = padding + index * (barWidth + barSpacing);
        if (x >= barX && x <= barX + barWidth) {
          newHoveredIndex = index;
        }
      });
      setHoveredIndex(newHoveredIndex);
    }
  };

  return (
    <div className="bg-card rounded-lg border border-border p-6">
      {title && (
        <h3 className="text-lg font-bold text-primary mb-2">{title}</h3>
      )}
      {description && (
        <p className="text-muted-foreground text-sm mb-4">{description}</p>
      )}
      
      <div className="relative">
        <canvas
          ref={canvasRef}
          width={400}
          height={300}
          className="w-full h-auto max-w-full cursor-pointer"
          onMouseMove={handleMouseMove}
          onMouseLeave={() => setHoveredIndex(null)}
        />
      </div>

      {/* Legend */}
      <div className="mt-4 flex flex-wrap gap-4 justify-center">
        {data.map((item, index) => (
          <div
            key={index}
            className="flex items-center gap-2 text-sm"
            onMouseEnter={() => setHoveredIndex(index)}
            onMouseLeave={() => setHoveredIndex(null)}
          >
            <div
              className="w-4 h-4 rounded"
              style={{ backgroundColor: item.color || '#6b7280' }}
            ></div>
            <span className="text-foreground">{item.label}: {item.value}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

// Pre-defined chart data for AAMI content
export const chartData = {
  deviceFailures: {
    title: 'الأسباب الشائعة لأعطال الأجهزة الطبية',
    description: 'توزيع أسباب الأعطال في الأجهزة الطبية حسب الإحصائيات العالمية',
    type: 'pie',
    data: [
      { label: 'عدم الصيانة', value: 35, color: '#ef4444', hoverColor: '#dc2626' },
      { label: 'سوء الاستخدام', value: 25, color: '#f59e0b', hoverColor: '#d97706' },
      { label: 'انتهاء العمر الافتراضي', value: 20, color: '#6b7280', hoverColor: '#4b5563' },
      { label: 'عيوب التصنيع', value: 12, color: '#8b5cf6', hoverColor: '#7c3aed' },
      { label: 'أسباب أخرى', value: 8, color: '#22c55e', hoverColor: '#16a34a' }
    ]
  },
  
  maintenanceFrequency: {
    title: 'تكرار الصيانة الوقائية حسب نوع الجهاز',
    description: 'عدد مرات الصيانة الوقائية المطلوبة سنوياً لكل نوع من الأجهزة الطبية',
    type: 'bar',
    data: [
      { label: 'أجهزة التنفس', value: 12, color: '#ef4444', hoverColor: '#dc2626' },
      { label: 'أجهزة المراقبة', value: 6, color: '#3b82f6', hoverColor: '#2563eb' },
      { label: 'أجهزة التصوير', value: 4, color: '#22c55e', hoverColor: '#16a34a' },
      { label: 'أجهزة المختبر', value: 8, color: '#f59e0b', hoverColor: '#d97706' },
      { label: 'أجهزة الجراحة', value: 10, color: '#8b5cf6', hoverColor: '#7c3aed' }
    ]
  },

  riskLevels: {
    title: 'توزيع مستويات المخاطر للأجهزة الطبية',
    description: 'تصنيف الأجهزة الطبية حسب مستوى المخاطر وفقاً لمعايير FDA',
    type: 'pie',
    data: [
      { label: 'مخاطر عالية (Class III)', value: 15, color: '#ef4444', hoverColor: '#dc2626' },
      { label: 'مخاطر متوسطة (Class II)', value: 45, color: '#f59e0b', hoverColor: '#d97706' },
      { label: 'مخاطر منخفضة (Class I)', value: 40, color: '#22c55e', hoverColor: '#16a34a' }
    ]
  }
};

const DataVisualization = ({ chartKey, customData }) => {
  const data = customData || chartData[chartKey];
  
  if (!data) {
    return (
      <div className="bg-card rounded-lg border border-border p-6 text-center">
        <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
        <p className="text-muted-foreground">لا توجد بيانات متاحة للعرض</p>
      </div>
    );
  }

  const getIcon = () => {
    switch (data.type) {
      case 'pie':
        return <PieChart className="h-5 w-5" />;
      case 'bar':
        return <BarChart3 className="h-5 w-5" />;
      default:
        return <TrendingUp className="h-5 w-5" />;
    }
  };

  return (
    <div className="w-full">
      <div className="flex items-center gap-2 mb-4">
        <div className="text-primary">
          {getIcon()}
        </div>
        <span className="text-sm text-muted-foreground">تصور البيانات</span>
      </div>
      
      <SimpleChart
        data={data.data}
        type={data.type}
        title={data.title}
        description={data.description}
      />
    </div>
  );
};

export default DataVisualization;
