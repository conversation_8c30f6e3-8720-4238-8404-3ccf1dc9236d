import React from 'react';
import { useAppContext } from '../../App.jsx';

const Footer = () => {
  const { handlePageChange } = useAppContext();

  const quickLinks = [
    { title: 'الأسس والمبادئ', page: 1 },
    { title: 'التطبيقات العملية', page: 21 },
    { title: 'المراجع والملاحق', page: 41 }
  ];

  return (
    <footer 
      className="bg-card border-t border-border mt-auto"
      role="contentinfo"
      aria-label="معلومات حقوق النشر والروابط السريعة"
    >
      <div className="p-4">
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          <div className="text-sm text-muted-foreground">
            <p>© 2025 د. محمد يعقوب اسماعيل - جامعة السودان للعلوم والتكنولوجيا</p>
            <p>قسم الهندسة الطبية الحيوية</p>
          </div>
          
          <nav aria-label="روابط سريعة للأقسام الرئيسية">
            <div className="flex gap-4">
              {quickLinks.map((link, index) => (
                <button
                  key={index}
                  onClick={() => handlePageChange(link.page)}
                  className="text-sm text-primary hover:text-primary/80 transition-colors focus:outline-none focus:ring-2 focus:ring-ring rounded px-2 py-1"
                  aria-label={`الانتقال إلى ${link.title}`}
                >
                  {link.title}
                </button>
              ))}
            </div>
          </nav>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
