import React, { useState } from 'react';
import { Calendar, Shield, Globe, Award, Users, Zap } from 'lucide-react';

const InteractiveTimeline = () => {
  const [selectedMilestone, setSelectedMilestone] = useState(null);

  const milestones = [
    {
      id: 1,
      year: '1967',
      title: 'تأسيس AAMI',
      description: 'تأسيس جمعية النهوض بالأجهزة الطبية (AAMI) لتطوير معايير السلامة والفعالية للأجهزة الطبية',
      icon: <Users className="h-6 w-6" />,
      color: 'bg-blue-500',
      details: 'بدأت AAMI كمنظمة غير ربحية مكرسة لتطوير وتعزيز معايير الأجهزة الطبية والتقنيات الصحية.'
    },
    {
      id: 2,
      year: '1977',
      title: 'اعتماد ANSI',
      description: 'حصول AAMI على اعتماد المعهد الأمريكي للمعايير الوطنية (ANSI) كمطور معايير',
      icon: <Award className="h-6 w-6" />,
      color: 'bg-green-500',
      details: 'هذا الاعتماد أعطى AAMI السلطة الرسمية لتطوير المعايير الوطنية الأمريكية للأجهزة الطبية.'
    },
    {
      id: 3,
      year: '1985',
      title: 'معايير السلامة الكهربائية',
      description: 'إطلاق أول معايير شاملة للسلامة الكهربائية للأجهزة الطبية',
      icon: <Zap className="h-6 w-6" />,
      color: 'bg-yellow-500',
      details: 'تطوير معايير IEC 60601 التي أصبحت الأساس العالمي لسلامة الأجهزة الطبية الكهربائية.'
    },
    {
      id: 4,
      year: '1995',
      title: 'التوسع العالمي',
      description: 'بداية التعاون الدولي وتطوير معايير عالمية مع منظمات دولية',
      icon: <Globe className="h-6 w-6" />,
      color: 'bg-purple-500',
      details: 'توسيع نطاق العمل ليشمل التعاون مع ISO وIEC لتطوير معايير عالمية موحدة.'
    },
    {
      id: 5,
      year: '2010',
      title: 'إدارة دورة الحياة',
      description: 'تطوير معايير شاملة لإدارة دورة حياة الأجهزة الطبية في المستشفيات',
      icon: <Shield className="h-6 w-6" />,
      color: 'bg-red-500',
      details: 'إدخال مفاهيم إدارة المخاطر وصيانة الأجهزة الطبية بشكل منهجي ومعياري.'
    },
    {
      id: 6,
      year: '2020',
      title: 'العصر الرقمي',
      description: 'تطوير معايير للأجهزة الذكية والذكاء الاصطناعي في الطب',
      icon: <Calendar className="h-6 w-6" />,
      color: 'bg-indigo-500',
      details: 'مواكبة التطورات التكنولوجية الحديثة مثل IoT والذكاء الاصطناعي في الأجهزة الطبية.'
    }
  ];

  return (
    <div className="w-full p-6 bg-card rounded-lg border border-border">
      <h3 className="text-xl font-bold text-primary mb-6 text-center">
        رحلة AAMI عبر الزمن
      </h3>
      
      {/* Timeline Container */}
      <div className="relative">
        {/* Timeline Line */}
        <div className="absolute top-8 left-0 right-0 h-1 bg-border rounded-full"></div>
        
        {/* Timeline Items */}
        <div className="flex justify-between items-start relative">
          {milestones.map((milestone, index) => (
            <div
              key={milestone.id}
              className="flex flex-col items-center cursor-pointer group"
              onClick={() => setSelectedMilestone(milestone)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  setSelectedMilestone(milestone);
                }
              }}
              tabIndex={0}
              role="button"
              aria-label={`عرض تفاصيل ${milestone.title} في عام ${milestone.year}`}
            >
              {/* Timeline Point */}
              <div className={`
                w-16 h-16 rounded-full ${milestone.color} 
                flex items-center justify-center text-white
                transform transition-all duration-300 hover:scale-110
                group-hover:shadow-lg z-10 relative
                ${selectedMilestone?.id === milestone.id ? 'scale-110 shadow-lg' : ''}
              `}>
                {milestone.icon}
              </div>
              
              {/* Year Label */}
              <div className="mt-3 text-sm font-bold text-primary">
                {milestone.year}
              </div>
              
              {/* Title */}
              <div className="mt-1 text-xs text-center max-w-20 text-muted-foreground group-hover:text-foreground transition-colors">
                {milestone.title}
              </div>
            </div>
          ))}
        </div>
      </div>
      
      {/* Selected Milestone Details */}
      {selectedMilestone && (
        <div className="mt-8 p-6 bg-muted rounded-lg animate-fade-in">
          <div className="flex items-start gap-4">
            <div className={`
              w-12 h-12 rounded-full ${selectedMilestone.color}
              flex items-center justify-center text-white flex-shrink-0
            `}>
              {selectedMilestone.icon}
            </div>
            <div className="flex-1">
              <h4 className="text-lg font-bold text-primary mb-2">
                {selectedMilestone.year} - {selectedMilestone.title}
              </h4>
              <p className="text-sm text-muted-foreground mb-3">
                {selectedMilestone.description}
              </p>
              <p className="text-sm text-foreground">
                {selectedMilestone.details}
              </p>
            </div>
            <button
              onClick={() => setSelectedMilestone(null)}
              className="text-muted-foreground hover:text-foreground transition-colors p-1"
              aria-label="إغلاق التفاصيل"
            >
              ✕
            </button>
          </div>
        </div>
      )}
      
      {!selectedMilestone && (
        <div className="mt-6 text-center text-sm text-muted-foreground">
          انقر على أي نقطة في الخط الزمني لعرض التفاصيل
        </div>
      )}
    </div>
  );
};

export default InteractiveTimeline;
