import React, { useState, useRef, useEffect } from 'react';
import { Book, Search, X, ExternalLink } from 'lucide-react';

// Glossary data
const glossaryTerms = {
  'AAMI': {
    fullName: 'Association for the Advancement of Medical Instrumentation',
    arabicName: 'جمعية النهوض بالأجهزة الطبية',
    definition: 'منظمة غير ربحية تطور معايير السلامة والفعالية للأجهزة الطبية والتقنيات الصحية',
    category: 'منظمات'
  },
  'AEM': {
    fullName: 'Automated External Defibrillator',
    arabicName: 'جهاز إزالة الرجفان الخارجي الآلي',
    definition: 'جهاز طبي محمول يحلل إيقاع القلب ويوفر صدمة كهربائية لعلاج السكتة القلبية',
    category: 'أجهزة طبية'
  },
  'SaMD': {
    fullName: 'Software as a Medical Device',
    arabicName: 'البرمجيات كجهاز طبي',
    definition: 'برمجيات مخصصة للاستخدام لغرض طبي واحد أو أكثر دون أن تكون جزءاً من جهاز طبي',
    category: 'تقنيات'
  },
  'TIR57': {
    fullName: 'Technical Information Report 57',
    arabicName: 'التقرير التقني رقم 57',
    definition: 'إرشادات AAMI لإدارة دورة حياة الأجهزة الطبية في المؤسسات الصحية',
    category: 'معايير'
  },
  'IEC 60601': {
    fullName: 'International Electrotechnical Commission Standard 60601',
    arabicName: 'معيار اللجنة الكهروتقنية الدولية 60601',
    definition: 'سلسلة من المعايير الدولية للسلامة الأساسية والأداء الأساسي للأجهزة الطبية الكهربائية',
    category: 'معايير'
  },
  'ISO 13485': {
    fullName: 'International Organization for Standardization 13485',
    arabicName: 'معيار المنظمة الدولية للمعايير 13485',
    definition: 'معيار دولي يحدد متطلبات نظام إدارة الجودة للأجهزة الطبية',
    category: 'معايير'
  },
  'CMMS': {
    fullName: 'Computerized Maintenance Management System',
    arabicName: 'نظام إدارة الصيانة المحوسب',
    definition: 'برنامج حاسوبي يساعد في تنظيم وإدارة أنشطة الصيانة الوقائية والتصحيحية',
    category: 'تقنيات'
  },
  'UDI': {
    fullName: 'Unique Device Identification',
    arabicName: 'معرف الجهاز الفريد',
    definition: 'نظام تعريف فريد للأجهزة الطبية يتضمن معرف الجهاز ومعرف الإنتاج',
    category: 'تنظيمات'
  }
};

// Tooltip component
const GlossaryTooltip = ({ term, children, position = 'top' }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState(position);
  const tooltipRef = useRef(null);
  const triggerRef = useRef(null);

  const termData = glossaryTerms[term];

  useEffect(() => {
    if (isVisible && tooltipRef.current && triggerRef.current) {
      const tooltip = tooltipRef.current;
      const trigger = triggerRef.current;
      const rect = trigger.getBoundingClientRect();
      const tooltipRect = tooltip.getBoundingClientRect();
      
      // Adjust position if tooltip goes off screen
      if (rect.top - tooltipRect.height < 10) {
        setTooltipPosition('bottom');
      } else if (rect.bottom + tooltipRect.height > window.innerHeight - 10) {
        setTooltipPosition('top');
      }
    }
  }, [isVisible]);

  if (!termData) return children;

  return (
    <span className="relative inline-block">
      <span
        ref={triggerRef}
        className="border-b border-dotted border-primary cursor-help text-primary hover:text-primary/80 transition-colors"
        onMouseEnter={() => setIsVisible(true)}
        onMouseLeave={() => setIsVisible(false)}
        onFocus={() => setIsVisible(true)}
        onBlur={() => setIsVisible(false)}
        tabIndex={0}
        role="button"
        aria-describedby={`tooltip-${term}`}
      >
        {children}
      </span>
      
      {isVisible && (
        <div
          ref={tooltipRef}
          id={`tooltip-${term}`}
          className={`
            absolute z-50 w-80 p-4 bg-card border border-border rounded-lg shadow-lg
            ${tooltipPosition === 'top' ? 'bottom-full mb-2' : 'top-full mt-2'}
            left-1/2 transform -translate-x-1/2
            animate-fade-in
          `}
          role="tooltip"
        >
          <div className="space-y-2">
            <div className="flex items-start justify-between">
              <h4 className="font-bold text-primary text-sm">{term}</h4>
              <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">
                {termData.category}
              </span>
            </div>
            
            <div className="text-xs text-muted-foreground">
              <p><strong>الاسم الكامل:</strong> {termData.fullName}</p>
              <p><strong>بالعربية:</strong> {termData.arabicName}</p>
            </div>
            
            <p className="text-sm text-foreground">{termData.definition}</p>
          </div>
          
          {/* Tooltip arrow */}
          <div className={`
            absolute left-1/2 transform -translate-x-1/2 w-2 h-2 bg-card border-border
            ${tooltipPosition === 'top' 
              ? 'top-full border-r border-b rotate-45' 
              : 'bottom-full border-l border-t rotate-45'
            }
          `} />
        </div>
      )}
    </span>
  );
};

// Main Glossary Panel
const GlossaryPanel = ({ isOpen, onClose }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = ['all', 'منظمات', 'أجهزة طبية', 'تقنيات', 'معايير', 'تنظيمات'];

  const filteredTerms = Object.entries(glossaryTerms).filter(([key, data]) => {
    const matchesSearch = key.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         data.arabicName.includes(searchTerm) ||
                         data.definition.includes(searchTerm);
    const matchesCategory = selectedCategory === 'all' || data.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-card rounded-lg border border-border max-w-4xl w-full max-h-[80vh] overflow-hidden animate-fade-in">
        {/* Header */}
        <div className="p-6 border-b border-border">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <Book className="h-6 w-6 text-primary" />
              <h2 className="text-xl font-bold text-primary">قاموس المصطلحات</h2>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-accent rounded-lg transition-colors"
              aria-label="إغلاق القاموس"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
          
          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <input
                type="text"
                placeholder="البحث في المصطلحات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-4 pr-10 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-ring"
              />
            </div>
            
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-4 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-ring"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'all' ? 'جميع الفئات' : category}
                </option>
              ))}
            </select>
          </div>
        </div>
        
        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {filteredTerms.length === 0 ? (
            <div className="text-center py-8">
              <Book className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
              <p className="text-muted-foreground">لم يتم العثور على مصطلحات مطابقة</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredTerms.map(([key, data]) => (
                <div key={key} className="p-4 bg-muted rounded-lg border border-border">
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="font-bold text-primary">{key}</h3>
                    <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">
                      {data.category}
                    </span>
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    <p><strong>الاسم الكامل:</strong> {data.fullName}</p>
                    <p><strong>بالعربية:</strong> {data.arabicName}</p>
                    <p className="text-muted-foreground">{data.definition}</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export { GlossaryTooltip, GlossaryPanel, glossaryTerms };
