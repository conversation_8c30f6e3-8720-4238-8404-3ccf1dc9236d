import React from 'react';
import { Book<PERSON>pen, ChevronLeft, ChevronRight, ZoomIn, ZoomOut, RotateCcw, Download } from 'lucide-react';
import { useAppContext } from '../../App.jsx';
import InteractiveContent from './InteractiveContent.jsx';
import pdfFile from '../../assets/الدليلالعمليلمعاييرAAMIفيالهندسةالسريرية.pdf';

const MainContent = () => {
  const { 
    currentPage, 
    totalPages, 
    zoomLevel, 
    handlePageChange, 
    handleZoom 
  } = useAppContext();

  return (
    <main 
      className="flex-1 bg-muted/30 flex flex-col"
      role="main"
      id="main-content"
      aria-label="المحتوى الرئيسي للكتاب"
    >
      {/* PDF Viewer Controls */}
      <div className="bg-card border-b border-border p-4 flex items-center justify-between sticky top-[73px] z-30">
        <div className="flex items-center gap-4">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="p-2 hover:bg-accent rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-ring"
            aria-label="الصفحة السابقة"
          >
            <ChevronRight className="h-5 w-5" />
          </button>
          
          <div className="flex items-center gap-2">
            <label htmlFor="page-input" className="text-sm">صفحة</label>
            <input
              id="page-input"
              type="number"
              value={currentPage}
              onChange={(e) => handlePageChange(parseInt(e.target.value))}
              className="w-16 px-2 py-1 text-center bg-background border border-border rounded focus:outline-none focus:ring-2 focus:ring-ring"
              min="1"
              max={totalPages}
              aria-label="رقم الصفحة الحالية"
            />
            <span className="text-sm">من {totalPages}</span>
          </div>
          
          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="p-2 hover:bg-accent rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-ring"
            aria-label="الصفحة التالية"
          >
            <ChevronLeft className="h-5 w-5" />
          </button>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleZoom('out')}
            className="p-2 hover:bg-accent rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-ring"
            aria-label="تصغير العرض"
          >
            <ZoomOut className="h-4 w-4" />
          </button>
          <span className="text-sm w-16 text-center" aria-label={`مستوى التكبير ${zoomLevel} بالمئة`}>
            {zoomLevel}%
          </span>
          <button
            onClick={() => handleZoom('in')}
            className="p-2 hover:bg-accent rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-ring"
            aria-label="تكبير العرض"
          >
            <ZoomIn className="h-4 w-4" />
          </button>
          <button
            onClick={() => handleZoom('reset')}
            className="p-2 hover:bg-accent rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-ring"
            aria-label="إعادة تعيين مستوى التكبير"
          >
            <RotateCcw className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Content Area */}
      <div className="flex-1 p-4 overflow-auto">
        <div className="max-w-6xl mx-auto space-y-6">
          {/* Interactive Content */}
          <InteractiveContent />

          {/* Traditional PDF Viewer */}
          <div className="flex justify-center">
            <div
              className="bg-white shadow-lg rounded-lg overflow-hidden pdf-container transition-transform duration-300"
              style={{ transform: `scale(${zoomLevel / 100})`, transformOrigin: 'top center' }}
            >
              <div className="w-[800px] h-[600px] bg-white border border-gray-200 flex items-center justify-center">
                <div className="text-center p-8">
                  <BookOpen className="h-16 w-16 text-primary mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-primary mb-2">الدليل العملي لمعايير AAMI</h3>
                  <p className="text-muted-foreground mb-4">في الهندسة السريرية</p>
                  <p className="text-sm text-muted-foreground mb-6">
                    صفحة {currentPage} من {totalPages}
                  </p>
                  <div className="bg-muted p-6 rounded-lg text-right">
                    <h4 className="font-semibold mb-3">محتوى الصفحة الحالية:</h4>
                    {currentPage === 1 && (
                      <div className="space-y-2 text-sm">
                        <p><strong>العنوان:</strong> الدليل العملي لمعايير AAMI في الهندسة السريرية</p>
                        <p><strong>المؤلف:</strong> د. محمد يعقوب اسماعيل</p>
                        <p><strong>المؤسسة:</strong> قسم الهندسة الطبية الحيوية، جامعة السودان للعلوم والتكنولوجيا (SUST)</p>
                        <p><strong>السنة:</strong> 2025</p>
                        <p><strong>الجزء الأول:</strong> الأسس والمبادئ</p>
                      </div>
                    )}
                    {currentPage === 2 && (
                      <div className="space-y-2 text-sm">
                        <p><strong>الفصل الأول:</strong> رحلة AAMI عبر الزمن</p>
                        <p><strong>أهداف التعلم:</strong></p>
                        <ul className="list-disc list-inside space-y-1 mr-4">
                          <li>وصف السياق التاريخي الذي أدى إلى تأسيس AAMI</li>
                          <li>تحديد المحطات الرئيسية في تطور برنامج معايير AAMI</li>
                          <li>ربط كل مرحلة من مراحل تطور AAMI بالتطورات الموازية في التكنولوجيا الطبية ودور المهندس السريري</li>
                        </ul>
                      </div>
                    )}
                    {currentPage > 2 && (
                      <div className="space-y-2 text-sm">
                        <p>هذه الصفحة تحتوي على محتوى من الكتاب الأصلي.</p>
                        <p>يمكنك تحميل ملف PDF الكامل للاطلاع على المحتوى التفصيلي.</p>
                      </div>
                    )}
                  </div>
                  <div className="mt-6">
                    <a
                      href={pdfFile}
                      download="AAMI_Clinical_Engineering_Guide.pdf"
                      className="inline-flex items-center gap-2 px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors focus:outline-none focus:ring-2 focus:ring-ring"
                      aria-label="تحميل الملف الكامل بصيغة PDF"
                    >
                      <Download className="h-4 w-4" />
                      تحميل الملف الكامل
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
};

export default MainContent;
