import React, { useState } from 'react';
import { Camera, Maximize2 } from 'lucide-react';
import Lightbox from './Lightbox.jsx';

const ImageGallery = ({ images, title, description, layout = 'grid' }) => {
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  const openLightbox = (index) => {
    setSelectedImageIndex(index);
    setLightboxOpen(true);
  };

  const closeLightbox = () => {
    setLightboxOpen(false);
  };

  if (!images || images.length === 0) {
    return (
      <div className="p-6 bg-muted rounded-lg border border-border text-center">
        <Camera className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
        <p className="text-muted-foreground">لا توجد صور متاحة</p>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* Gallery Header */}
      {(title || description) && (
        <div className="mb-6">
          {title && (
            <h3 className="text-lg font-bold text-primary mb-2">{title}</h3>
          )}
          {description && (
            <p className="text-muted-foreground text-sm">{description}</p>
          )}
        </div>
      )}

      {/* Gallery Grid */}
      <div className={`
        ${layout === 'grid' 
          ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4' 
          : 'flex flex-wrap gap-4'
        }
      `}>
        {images.map((image, index) => (
          <div
            key={index}
            className="group relative bg-card rounded-lg overflow-hidden border border-border hover:shadow-lg transition-all duration-300 cursor-pointer"
            onClick={() => openLightbox(index)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                openLightbox(index);
              }
            }}
            tabIndex={0}
            role="button"
            aria-label={`عرض ${image.title || `الصورة ${index + 1}`} في المعرض`}
          >
            {/* Image Container */}
            <div className="relative aspect-video overflow-hidden">
              <img
                src={image.thumbnail || image.src}
                alt={image.alt}
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                loading="lazy"
              />
              
              {/* Overlay */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <Maximize2 className="h-8 w-8 text-white drop-shadow-lg" />
                </div>
              </div>
              
              {/* Image Counter */}
              <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                {index + 1} / {images.length}
              </div>
            </div>

            {/* Image Info */}
            {(image.title || image.caption) && (
              <div className="p-3">
                {image.title && (
                  <h4 className="font-semibold text-foreground text-sm mb-1 line-clamp-1">
                    {image.title}
                  </h4>
                )}
                {image.caption && (
                  <p className="text-muted-foreground text-xs line-clamp-2">
                    {image.caption}
                  </p>
                )}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Lightbox */}
      <Lightbox
        images={images}
        isOpen={lightboxOpen}
        onClose={closeLightbox}
        initialIndex={selectedImageIndex}
      />
    </div>
  );
};

// Sample images for demonstration
export const sampleImages = {
  clinicalEngineering: [
    {
      src: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=800&h=600&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=300&fit=crop',
      title: 'مهندس سريري يقوم بفحص جهاز طبي',
      caption: 'فحص دوري لجهاز مراقبة المريض في وحدة العناية المركزة',
      alt: 'مهندس سريري يفحص جهاز مراقبة طبي'
    },
    {
      src: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=800&h=600&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=300&fit=crop',
      title: 'اختبار السلامة الكهربائية',
      caption: 'إجراء اختبارات السلامة الكهربائية وفقاً لمعايير IEC 60601',
      alt: 'اختبار السلامة الكهربائية للأجهزة الطبية'
    },
    {
      src: 'https://images.unsplash.com/photo-1551601651-2a8555f1a136?w=800&h=600&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-1551601651-2a8555f1a136?w=400&h=300&fit=crop',
      title: 'ملصق UDI على جهاز طبي',
      caption: 'مثال على ملصق معرف الجهاز الفريد (UDI) المطلوب حسب معايير FDA',
      alt: 'ملصق UDI على جهاز طبي'
    },
    {
      src: 'https://images.unsplash.com/photo-1504813184591-01572f98c85f?w=800&h=600&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-1504813184591-01572f98c85f?w=400&h=300&fit=crop',
      title: 'واجهة نظام إدارة الأجهزة الطبية (CMMS)',
      caption: 'لقطة شاشة من نظام إدارة الصيانة المحوسب للأجهزة الطبية',
      alt: 'واجهة نظام CMMS'
    },
    {
      src: 'https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=800&h=600&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=400&h=300&fit=crop',
      title: 'ورشة صيانة الأجهزة الطبية',
      caption: 'ورشة مجهزة بالكامل لصيانة وإصلاح الأجهزة الطبية',
      alt: 'ورشة صيانة الأجهزة الطبية'
    },
    {
      src: 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=800&h=600&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=400&h=300&fit=crop',
      title: 'تدريب على استخدام الأجهزة الطبية',
      caption: 'جلسة تدريبية للطاقم الطبي على استخدام جهاز جديد',
      alt: 'تدريب الطاقم الطبي'
    }
  ]
};

export default ImageGallery;
